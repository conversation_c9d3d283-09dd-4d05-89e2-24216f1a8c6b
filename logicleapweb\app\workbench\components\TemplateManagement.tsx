'use client';

import React, { useState, useEffect } from 'react';
import { Search, Upload, Plus } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { useTemplate } from '../contexts/TemplateContext';
import PermissionTemplateModal from './PermissionTemplateModal';
import { GetNotification } from 'logic-common/dist/components/Notification';
import {
  fetchMyTemplates,
  fetchOfficialTemplates,
  fetchCurrentTemplate,
  fetchFolders,
  fetchFolderTemplates,
  applyTemplate,
  removeTemplate,
  getTemplateDetails,
  filterTemplates,
  initTemplateModalState,
  Template as TemplateType,
  Folder,
  TemplateModalState
} from '../utils';
import './TemplateManagement.css';

interface School {
  id: number;
  schoolName: string;
  province: string;
  city: string;
  district: string;
}

interface TemplateManagementProps {
  selectedSchool?: School | null;
  userInfo?: {
    id: number;
    nickName: string;
    [key: string]: any;
  };
}

const TemplateManagement: React.FC<TemplateManagementProps> = ({
  selectedSchool,
  userInfo
}) => {
  const [activeTab, setActiveTab] = useState<'my' | 'official'>('official');
  const [searchQuery, setSearchQuery] = useState('');
  const [templates, setTemplates] = useState<TemplateType[]>([]);
  const [loading, setLoading] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<number | null>(null);
  const [currentTemplate, setCurrentTemplate] = useState<{ templateId: number } | null>(null);
  const [officialTemplateCount, setOfficialTemplateCount] = useState<number>(0);
  const [showOfficialDetail, setShowOfficialDetail] = useState(false);
  const [detailTemplates, setDetailTemplates] = useState<TemplateType[]>([]);

  // 文件夹相关状态
  const [folders, setFolders] = useState<any[]>([]);
  const [currentView, setCurrentView] = useState<'folders' | 'templates'>('folders');
  const [currentFolder, setCurrentFolder] = useState<any>(null);

  const [loadingFolders, setLoadingFolders] = useState(false);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);

  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);

  // 使用模板上下文
  const { refreshCurrentTemplate, notifyGlobalTemplateChange } = useTemplate();

  // 获取通知组件
  const notification = GetNotification();

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取我的模板 - 使用工具函数
  const getMyTemplates = async () => {
    return await fetchMyTemplates(userId);
  };

  // 获取官方模板数量 - 使用真实数据
  const getOfficialTemplateCount = async () => {
    try {
      const templates = await fetchOfficialTemplates();
      setOfficialTemplateCount(templates.length);
    } catch (error) {
      console.error('获取官方模板数量失败:', error);
      setOfficialTemplateCount(0);
    }
  };

  // 获取详细的官方模板列表 - 使用真实数据
  const getDetailOfficialTemplates = async () => {
    try {
      const templates = await fetchOfficialTemplates();
      setDetailTemplates(templates);
    } catch (error) {
      console.error('获取官方模板列表失败:', error);
      setDetailTemplates([]);
    }
  };

  // 处理模板点击事件
  const handleTemplateClick = (template: TemplateType) => {
    if (template.isOfficial && template.id === 2) {
      // 课程模板，显示详细页面
      setShowOfficialDetail(true);
      getDetailOfficialTemplates();
    } else if (!template.isOfficial) {
      // 我的模板，进入编辑模式
      handleEditTemplate(template);
    }
  };

  // 返回主页面
  const handleBackToMain = () => {
    setShowOfficialDetail(false);
    setDetailTemplates([]);
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setTemplates([]); // 清空模板列表数据
  };

  // 处理文件夹点击 - 使用工具函数
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setTemplates([]); // 先清空之前的数据
    setLoadingFolderTemplates(true); // 开始加载时显示加载动画

    try {
      const folderTemplates = await fetchFolderTemplates(folder.id);
      setTemplates(folderTemplates);
    } catch (error) {
      console.error('获取文件夹模板失败:', error);
      setTemplates([]);
    } finally {
      setLoadingFolderTemplates(false);
    }
  };

  // 获取文件夹列表 - 使用工具函数
  const getFolders = async () => {
    setLoadingFolders(true);
    try {
      const foldersData = await fetchFolders();
      setFolders(foldersData);
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
      setFolders([]);
    } finally {
      setLoadingFolders(false);
    }
  };



  // 获取官方模板 - 使用工具函数
  const getOfficialTemplates = async () => {
    return await fetchOfficialTemplates();
  };

  // 获取当前使用的模板 - 使用工具函数
  const getCurrentTemplate = async () => {
    if (!userId) return;

    try {
      const currentTemplate = await fetchCurrentTemplate(userId);
      setCurrentTemplate(currentTemplate);

      // 如果没有当前模板，可能需要显示默认模板提示
      if (!currentTemplate) {
        console.log('用户尚未设置模板，将显示默认模板选项');
      }
    } catch (error) {
      console.error('获取当前使用模板失败:', error);
      setCurrentTemplate(null);
    }
  };

  // 获取所有模板 - 使用工具函数
  const getAllTemplates = async () => {
    if (!mounted || !userId) return;

    setLoading(true);
    try {
      const [myTemplates, officialTemplates] = await Promise.all([
        getMyTemplates(),
        getOfficialTemplates()
      ]);

      setTemplates([...myTemplates, ...officialTemplates]);
    } catch (error) {
      console.error('获取模板列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (mounted && userId) {
      if (activeTab === 'my') {
        getAllTemplates();
      } else {
        getFolders();
      }
      getCurrentTemplate();
      getOfficialTemplateCount();
    }
  }, [mounted, userId, activeTab]);

  // 处理创建模板成功
  const handleCreateTemplateSuccess = () => {
    getAllTemplates(); // 刷新模板列表
    setIsCreateModalOpen(false);
  };

  // 处理编辑模板 - 使用工具函数
  const handleEditTemplate = async (template: TemplateType) => {
    try {
      // 检查模板详情是否可以获取
      const templateDetails = await getTemplateDetails(template.id);
      if (templateDetails) {
        setEditingTemplateId(template.id);
        setIsEditModalOpen(true);
      } else {
        console.error('获取模板详情失败');
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
    }
  };

  // 处理编辑模板成功
  const handleEditTemplateSuccess = () => {
    getAllTemplates(); // 刷新模板列表
    setIsEditModalOpen(false);
    setEditingTemplateId(null);
  };

  // 处理使用模板 - 使用工具函数
  const handleUseTemplate = async (templateId: number) => {
    try {
      const success = await applyTemplate(templateId, userId, roleId || 2);

      if (success) {
        // 刷新当前使用的模板状态
        getCurrentTemplate();

        // 通知全局模板变化，触发所有班级学生模板重置
        notifyGlobalTemplateChange();
      }
    } catch (error) {
      console.error('应用模板失败:', error);
    }
  };

  // 处理删除模板 - 使用工具函数
  const handleDeleteTemplate = async (templateId: number) => {
    try {
      const success = await removeTemplate(templateId);
      if (success) {
        console.log('删除模板成功');
        getAllTemplates(); // 刷新模板列表
        setIsEditModalOpen(false);
        setEditingTemplateId(null);
      }
    } catch (error) {
      console.error('删除模板失败:', error);
    }
  };

  const filteredTemplates = filterTemplates(templates, activeTab, searchQuery);

  return (
    <div className="template-management">
     

      {/* 标签页切换 */}
      <div className="template-tabs">
        <div className={`template-tabs-group ${activeTab === 'my' ? 'official-active' : ''}`}>
          <button
            className="tab-btn"
            onClick={() => setActiveTab('official')}
          >
            官方模板
          </button>
          <button
            className={`tab-btn ${activeTab === 'my' ? 'active' : ''}`}
            onClick={() => setActiveTab('my')}
          >
            我的模板
          </button>
        </div>
        {activeTab === 'my' && (
          <button className="create-template-btn" onClick={() => setIsCreateModalOpen(true)}>
            <div className="create-template-icon">
              <Plus size={16} />
            </div>
            创建模板
          </button>
        )}
      </div>

      {/* 模板网格 */}
      {showOfficialDetail ? (
        <div>
          <div className="detail-header">
            <button className="back-btn" onClick={handleBackToMain}>
              ← 返回
            </button>
          </div>
          <div className="template-grid">
            {detailTemplates.map((template) => (
              <div
                key={template.id}
                className="template-card detail-template-card"
                onClick={() => handleUseTemplate(template.id)}
                style={{ cursor: 'pointer' }}
              >
                <div className="template-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="template-info">
                  <div className="template-title">
                    <div className="template-name-with-badges">
                      <span className="template-name">{template.templateName}</span>
                      <span className="template-type official">官方</span>
                    </div>
                    <div className="template-meta">
                      <span className="create-time">
                        创建于{new Date(template.createTime).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="template-actions">
                  <div className="usage-info">暂无学生使用</div>
                  <button
                    className="use-template-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUseTemplate(template.id);
                    }}
                  >
                    使用此模板
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className={`template-grid ${activeTab === 'my' ? 'my-templates' : ''}`}>
          {activeTab === 'official' && currentView === 'folders' ? (
            // 文件夹视图
            loadingFolders ? (
              <div className="loading-placeholder">
                <div className="loading-spinner"></div>
                <p>正在加载文件夹...</p>
              </div>
            ) : (
              <div className="folder-grid">
                {/* 文件夹列表 */}
                {folders.map(folder => (
                  <div
                    key={folder.id}
                    className="folder-card"
                    onClick={() => handleFolderClick(folder)}
                  >
                    <div className="folder-card-content">
                      <div className="folder-card-left">
                        <div className="folder-icon">
                          <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z" fill="#F97316" stroke="#F97316" strokeWidth="2" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <div className="folder-info">
                          <h3 className="folder-name">
                            {folder.folderName}
                            <span className="official-tag">官方</span>
                          </h3>
                          <p className="folder-count">
                            {folder.templateCount || 0} 个模板
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : activeTab === 'official' && currentView === 'templates' ? (
            // 文件夹内模板视图
            <div>
              <div className="folder-header">
                <button className="back-btn" onClick={handleBackToFolders}>
                  ← 返回
                </button>
              </div>

              {loadingFolderTemplates ? (
                <div className="loading-placeholder">
                  <div className="loading-spinner"></div>
                  <p>正在加载模板...</p>
                </div>
              ) : templates.length === 0 ? (
                <div className="empty-placeholder">
                  <p>当前文件夹暂无模板</p>
                </div>
              ) : (
                <div className="template-list">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className="template-card"
                      onClick={() => handleTemplateClick(template)}
                    >
                      <div className="template-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <div className="template-info">
                        <div className="template-title">
                          <div className="template-name-with-badges">
                            <span className="template-name">{template.templateName}</span>
                            <span className="template-type official">官方</span>
                          </div>
                          <div className="template-meta">
                            <span className="create-time">
                              创建于{new Date(template.createTime).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="template-actions">
                        <div className="usage-info">暂无学生使用</div>
                        <button
                          className={`use-template-btn ${currentTemplate?.templateId === template.id ? 'current' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUseTemplate(template.id);
                          }}
                        >
                          {currentTemplate?.templateId === template.id ? '当前使用' : '使用此模板'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 我的模板视图
            loading ? (
              <div className="loading-placeholder">
                <div className="loading-spinner"></div>
                <p>正在加载模板...</p>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="empty-placeholder">
                <p>暂无{activeTab === 'my' ? '我的' : '官方'}模板</p>
              </div>
            ) : (
            <>
              {/* 默认模板显示 - 仅在没有当前模板时显示 */}
              {!currentTemplate && activeTab === 'official' && (
                <div className="template-card default-template-card">
                  <div className="template-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#10B981" stroke="#10B981" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="template-info">
                    <div className="template-title">
                      <div className="template-name-with-badges">
                        <span className="template-name">基础默认模板</span>
                        <span className="template-type official">系统默认</span>
                        <span className="current-template-badge default">当前使用</span>
                      </div>
                      <div className="template-meta">
                        <span className="create-time">系统提供的基础权限模板</span>
                      </div>
                    </div>
                  </div>
                  <div className="template-actions">
                    <div className="usage-info">系统默认</div>
                    <button className="use-template-btn current" disabled>
                      当前使用
                    </button>
                  </div>
                </div>
              )}

              {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className={`template-card ${!template.isOfficial || template.id === 2 ? 'editable' : ''}`}
              onClick={() => handleTemplateClick(template)}
              style={{ cursor: !template.isOfficial || template.id === 2 ? 'pointer' : 'default' }}
            >
              <div className="template-icon">
                {template.isOfficial ? (
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                ) : (
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="7" height="7" rx="1" fill="white"/>
                    <rect x="14" y="3" width="7" height="7" rx="1" fill="white"/>
                    <rect x="3" y="14" width="7" height="7" rx="1" fill="white"/>
                    <rect x="14" y="14" width="7" height="7" rx="1" fill="white"/>
                  </svg>
                )}
              </div>
              <div className="template-info">
                <div className="template-title">
                  <div className="template-name-with-badges">
                    <span className="template-name">{template.templateName}</span>
                    <span className={`template-type ${template.isOfficial ? 'official' : ''}`}>
                      {template.isOfficial ? '官方' : '自定义'}
                    </span>
                    {currentTemplate?.templateId === template.id && (
                      <span className="current-template-badge">当前使用</span>
                    )}
                  </div>
                  <div className="template-meta">
                    <span className="create-time">
                      {template.id === 2 ? `${officialTemplateCount} 个模板` : `创建于${new Date(template.createTime).toLocaleDateString()}`}
                    </span>
                  </div>
                </div>
                {!template.isOfficial && (
                  <div className="edit-hint">
                    <span>点击编辑模板</span>
                  </div>
                )}
              </div>
              <div className="template-actions">
                {/* 课程模板（id为2）不显示使用情况信息 */}
                {template.id !== 2 && (
                  <div className="usage-info">
                    {template.usageCount ? `${template.usageCount}名学生使用` : '暂无学生使用'}
                  </div>
                )}
                {/* 课程模板（id为2）不显示使用此模板按钮 */}
                {template.id !== 2 && (
                  <button
                    className={`use-template-btn ${currentTemplate?.templateId === template.id ? 'current' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      if (currentTemplate?.templateId !== template.id) {
                        handleUseTemplate(template.id);
                      }
                    }}
                    disabled={currentTemplate?.templateId === template.id}
                  >
                    {currentTemplate?.templateId === template.id ? '当前使用' : '使用此模板'}
                  </button>
                )}
              </div>
            </div>
          ))
            )
          )}
        </div>
      )}

      {/* 创建模板弹窗 */}
      <PermissionTemplateModal
        visible={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateTemplateSuccess}
        roleId={roleId || 2} // 默认为教师角色
        userId={userId || 0}
      />

      {/* 编辑模板弹窗 */}
      {isEditModalOpen && editingTemplateId && (
        <PermissionTemplateModal
          visible={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingTemplateId(null);
          }}
          onSuccess={handleEditTemplateSuccess}
          onDeleteTemplate={handleDeleteTemplate}
          roleId={roleId || 2}
          userId={userId || 0}
          templateId={editingTemplateId}
        />
      )}
    </div>
  );
};

export default TemplateManagement;
