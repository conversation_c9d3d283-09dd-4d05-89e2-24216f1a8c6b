"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx":
/*!*********************************************************!*\
  !*** ./app/workbench/components/TemplateManagement.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-redux */ \"(app-pages-browser)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _PermissionTemplateModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PermissionTemplateModal */ \"(app-pages-browser)/./app/workbench/components/PermissionTemplateModal.tsx\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./app/workbench/utils/index.ts\");\n/* harmony import */ var _TemplateManagement_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TemplateManagement.css */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst TemplateManagement = (param)=>{\n    let { selectedSchool, userInfo } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"official\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTemplateId, setEditingTemplateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentTemplate, setCurrentTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [officialTemplateCount, setOfficialTemplateCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOfficialDetail, setShowOfficialDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [detailTemplates, setDetailTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 文件夹相关状态\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"folders\");\n    const [currentFolder, setCurrentFolder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingFolders, setLoadingFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingFolderTemplates, setLoadingFolderTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userId = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector)((state)=>state.user.userState.userId);\n    const roleId = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector)((state)=>state.user.userState.roleId);\n    // 使用模板上下文\n    const { refreshCurrentTemplate, notifyGlobalTemplateChange } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_2__.useTemplate)();\n    // 获取通知组件\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_4__.GetNotification)();\n    // 防止水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    // 获取我的模板 - 使用工具函数\n    const getMyTemplates = async ()=>{\n        return await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchMyTemplates)(userId);\n    };\n    // 获取官方模板数量 - 使用真实数据\n    const getOfficialTemplateCount = async ()=>{\n        try {\n            const templates = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchOfficialTemplates)();\n            setOfficialTemplateCount(templates.length);\n        } catch (error) {\n            console.error(\"获取官方模板数量失败:\", error);\n            setOfficialTemplateCount(0);\n        }\n    };\n    // 获取详细的官方模板列表 - 使用真实数据\n    const getDetailOfficialTemplates = async ()=>{\n        try {\n            const templates = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchOfficialTemplates)();\n            setDetailTemplates(templates);\n        } catch (error) {\n            console.error(\"获取官方模板列表失败:\", error);\n            setDetailTemplates([]);\n        }\n    };\n    // 处理模板点击事件\n    const handleTemplateClick = (template)=>{\n        if (template.isOfficial && template.id === 2) {\n            // 课程模板，显示详细页面\n            setShowOfficialDetail(true);\n            getDetailOfficialTemplates();\n        } else if (!template.isOfficial) {\n            // 我的模板，进入编辑模式\n            handleEditTemplate(template);\n        }\n    };\n    // 返回主页面\n    const handleBackToMain = ()=>{\n        setShowOfficialDetail(false);\n        setDetailTemplates([]);\n    };\n    // 返回文件夹列表\n    const handleBackToFolders = ()=>{\n        setCurrentView(\"folders\");\n        setCurrentFolder(null);\n        setTemplates([]); // 清空模板列表数据\n    };\n    // 处理文件夹点击 - 使用工具函数\n    const handleFolderClick = async (folder)=>{\n        setCurrentFolder(folder);\n        setCurrentView(\"templates\");\n        setTemplates([]); // 先清空之前的数据\n        setLoadingFolderTemplates(true); // 开始加载时显示加载动画\n        try {\n            const folderTemplates = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchFolderTemplates)(folder.id);\n            setTemplates(folderTemplates);\n        } catch (error) {\n            console.error(\"获取文件夹模板失败:\", error);\n            setTemplates([]);\n        } finally{\n            setLoadingFolderTemplates(false);\n        }\n    };\n    // 获取文件夹列表 - 使用工具函数\n    const getFolders = async ()=>{\n        setLoadingFolders(true);\n        try {\n            const foldersData = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchFolders)();\n            setFolders(foldersData);\n        } catch (error) {\n            console.error(\"获取文件夹列表失败:\", error);\n            setFolders([]);\n        } finally{\n            setLoadingFolders(false);\n        }\n    };\n    // 获取官方模板 - 使用工具函数\n    const getOfficialTemplates = async ()=>{\n        return await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchOfficialTemplates)();\n    };\n    // 获取当前使用的模板 - 使用工具函数\n    const getCurrentTemplate = async ()=>{\n        if (!userId) return;\n        try {\n            const currentTemplate = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchCurrentTemplate)(userId);\n            setCurrentTemplate(currentTemplate);\n            // 如果没有当前模板，可能需要显示默认模板提示\n            if (!currentTemplate) {\n                console.log(\"用户尚未设置模板，将显示默认模板选项\");\n            }\n        } catch (error) {\n            console.error(\"获取当前使用模板失败:\", error);\n            setCurrentTemplate(null);\n        }\n    };\n    // 获取所有模板 - 使用工具函数\n    const getAllTemplates = async ()=>{\n        if (!mounted || !userId) return;\n        setLoading(true);\n        try {\n            const [myTemplates, officialTemplates] = await Promise.all([\n                getMyTemplates(),\n                getOfficialTemplates()\n            ]);\n            setTemplates([\n                ...myTemplates,\n                ...officialTemplates\n            ]);\n        } catch (error) {\n            console.error(\"获取模板列表失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted && userId) {\n            if (activeTab === \"my\") {\n                getAllTemplates();\n            } else {\n                getFolders();\n            }\n            getCurrentTemplate();\n            getOfficialTemplateCount();\n        }\n    }, [\n        mounted,\n        userId,\n        activeTab\n    ]);\n    // 处理创建模板成功\n    const handleCreateTemplateSuccess = ()=>{\n        getAllTemplates(); // 刷新模板列表\n        setIsCreateModalOpen(false);\n    };\n    // 处理编辑模板 - 使用工具函数\n    const handleEditTemplate = async (template)=>{\n        try {\n            // 检查模板详情是否可以获取\n            const templateDetails = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.getTemplateDetails)(template.id);\n            if (templateDetails) {\n                setEditingTemplateId(template.id);\n                setIsEditModalOpen(true);\n            } else {\n                console.error(\"获取模板详情失败\");\n            }\n        } catch (error) {\n            console.error(\"获取模板详情失败:\", error);\n        }\n    };\n    // 处理编辑模板成功\n    const handleEditTemplateSuccess = ()=>{\n        getAllTemplates(); // 刷新模板列表\n        setIsEditModalOpen(false);\n        setEditingTemplateId(null);\n    };\n    // 处理使用模板 - 使用工具函数\n    const handleUseTemplate = async (templateId)=>{\n        try {\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.applyTemplate)(templateId, userId, roleId || 2);\n            if (success) {\n                // 刷新当前使用的模板状态\n                getCurrentTemplate();\n                // 通知全局模板变化，触发所有班级学生模板重置\n                notifyGlobalTemplateChange();\n            }\n        } catch (error) {\n            console.error(\"应用模板失败:\", error);\n        }\n    };\n    // 处理删除模板 - 使用工具函数\n    const handleDeleteTemplate = async (templateId)=>{\n        try {\n            const success = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.removeTemplate)(templateId);\n            if (success) {\n                console.log(\"删除模板成功\");\n                getAllTemplates(); // 刷新模板列表\n                setIsEditModalOpen(false);\n                setEditingTemplateId(null);\n            }\n        } catch (error) {\n            console.error(\"删除模板失败:\", error);\n        }\n    };\n    const filteredTemplates = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.filterTemplates)(templates, activeTab, searchQuery);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"template-management\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"template-tabs\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"template-tabs-group \".concat(activeTab === \"my\" ? \"official-active\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"tab-btn\",\n                                onClick: ()=>setActiveTab(\"official\"),\n                                children: \"官方模板\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"tab-btn \".concat(activeTab === \"my\" ? \"active\" : \"\"),\n                                onClick: ()=>setActiveTab(\"my\"),\n                                children: \"我的模板\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined),\n                    activeTab === \"my\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"create-template-btn\",\n                        onClick: ()=>setIsCreateModalOpen(true),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"create-template-icon\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            \"创建模板\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, undefined),\n            showOfficialDetail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"detail-header\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"back-btn\",\n                            onClick: handleBackToMain,\n                            children: \"← 返回\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"template-grid\",\n                        children: detailTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-card detail-template-card\",\n                                onClick: ()=>handleUseTemplate(template.id),\n                                style: {\n                                    cursor: \"pointer\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"24\",\n                                            height: \"24\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\",\n                                                fill: \"#FFB800\",\n                                                stroke: \"#FFB800\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-info\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-title\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"template-name-with-badges\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"template-name\",\n                                                            children: template.templateName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"template-type official\",\n                                                            children: \"官方\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"template-meta\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"create-time\",\n                                                        children: [\n                                                            \"创建于\",\n                                                            new Date(template.createTime).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"usage-info\",\n                                                children: \"暂无学生使用\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"use-template-btn\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleUseTemplate(template.id);\n                                                },\n                                                children: \"使用此模板\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, template.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"template-grid \".concat(activeTab === \"my\" ? \"my-templates\" : \"\"),\n                children: activeTab === \"official\" && currentView === \"folders\" ? // 文件夹视图\n                loadingFolders ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-placeholder\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在加载文件夹...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 15\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"folder-grid\",\n                    children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"folder-card\",\n                            onClick: ()=>handleFolderClick(folder),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"folder-card-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"folder-card-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"folder-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 48 48\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z\",\n                                                    fill: \"#F97316\",\n                                                    stroke: \"#F97316\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 29\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 27\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 25\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"folder-info\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"folder-name\",\n                                                    children: [\n                                                        folder.folderName,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"official-tag\",\n                                                            children: \"官方\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 27\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"folder-count\",\n                                                    children: [\n                                                        folder.templateCount || 0,\n                                                        \" 个模板\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 23\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 21\n                            }, undefined)\n                        }, folder.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 19\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 15\n                }, undefined) : activeTab === \"official\" && currentView === \"templates\" ? // 文件夹内模板视图\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"folder-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"back-btn\",\n                                onClick: handleBackToFolders,\n                                children: \"← 返回\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 15\n                        }, undefined),\n                        loadingFolderTemplates ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-placeholder\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading-spinner\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"正在加载模板...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 17\n                        }, undefined) : templates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"empty-placeholder\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"当前文件夹暂无模板\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 19\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 17\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"template-list\",\n                            children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"template-card\",\n                                    onClick: ()=>handleTemplateClick(template),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\",\n                                                    fill: \"#FFB800\",\n                                                    stroke: \"#FFB800\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-info\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-title\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-name-with-badges\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"template-name\",\n                                                                children: template.templateName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"template-type official\",\n                                                                children: \"官方\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"template-meta\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"create-time\",\n                                                            children: [\n                                                                \"创建于\",\n                                                                new Date(template.createTime).toLocaleDateString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"template-actions\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"usage-info\",\n                                                    children: \"暂无学生使用\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"use-template-btn \".concat((currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id ? \"current\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        handleUseTemplate(template.id);\n                                                    },\n                                                    children: (currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id ? \"当前使用\" : \"使用此模板\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, template.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 21\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 425,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 13\n                }, undefined) : // 我的模板视图\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-placeholder\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"正在加载模板...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 15\n                }, undefined) : filteredTemplates.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"empty-placeholder\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"暂无\",\n                            activeTab === \"my\" ? \"我的\" : \"官方\",\n                            \"模板\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 15\n                }, undefined) : filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"template-card \".concat(!template.isOfficial || template.id === 2 ? \"editable\" : \"\"),\n                        onClick: ()=>handleTemplateClick(template),\n                        style: {\n                            cursor: !template.isOfficial || template.id === 2 ? \"pointer\" : \"default\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-icon\",\n                                children: template.isOfficial ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"24\",\n                                    height: \"24\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z\",\n                                        fill: \"#FFB800\",\n                                        stroke: \"#FFB800\",\n                                        strokeWidth: \"2\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    width: \"24\",\n                                    height: \"24\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            x: \"3\",\n                                            y: \"3\",\n                                            width: \"7\",\n                                            height: \"7\",\n                                            rx: \"1\",\n                                            fill: \"white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            x: \"14\",\n                                            y: \"3\",\n                                            width: \"7\",\n                                            height: \"7\",\n                                            rx: \"1\",\n                                            fill: \"white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            x: \"3\",\n                                            y: \"14\",\n                                            width: \"7\",\n                                            height: \"7\",\n                                            rx: \"1\",\n                                            fill: \"white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            x: \"14\",\n                                            y: \"14\",\n                                            width: \"7\",\n                                            height: \"7\",\n                                            rx: \"1\",\n                                            fill: \"white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-info\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"template-title\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-name-with-badges\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"template-name\",\n                                                        children: template.templateName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"template-type \".concat(template.isOfficial ? \"official\" : \"\"),\n                                                        children: template.isOfficial ? \"官方\" : \"自定义\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    (currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"current-template-badge\",\n                                                        children: \"当前使用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"template-meta\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"create-time\",\n                                                    children: template.id === 2 ? \"\".concat(officialTemplateCount, \" 个模板\") : \"创建于\".concat(new Date(template.createTime).toLocaleDateString())\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    !template.isOfficial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"edit-hint\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"点击编辑模板\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"template-actions\",\n                                children: [\n                                    template.id !== 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"usage-info\",\n                                        children: template.usageCount ? \"\".concat(template.usageCount, \"名学生使用\") : \"暂无学生使用\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    template.id !== 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"use-template-btn \".concat((currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id ? \"current\" : \"\"),\n                                        onClick: (e)=>{\n                                            e.stopPropagation(); // 阻止事件冒泡\n                                            if ((currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) !== template.id) {\n                                                handleUseTemplate(template.id);\n                                            }\n                                        },\n                                        disabled: (currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id,\n                                        children: (currentTemplate === null || currentTemplate === void 0 ? void 0 : currentTemplate.templateId) === template.id ? \"当前使用\" : \"使用此模板\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, template.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PermissionTemplateModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSuccess: handleCreateTemplateSuccess,\n                roleId: roleId || 2,\n                userId: userId || 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, undefined),\n            isEditModalOpen && editingTemplateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PermissionTemplateModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                visible: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingTemplateId(null);\n                },\n                onSuccess: handleEditTemplateSuccess,\n                onDeleteTemplate: handleDeleteTemplate,\n                roleId: roleId || 2,\n                userId: userId || 0,\n                templateId: editingTemplateId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n                lineNumber: 564,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\LL\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\TemplateManagement.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateManagement, \"eqc9tBFxXa0vEHPJ44lTih0s1RM=\", false, function() {\n    return [\n        react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector,\n        react_redux__WEBPACK_IMPORTED_MODULE_7__.useSelector,\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_2__.useTemplate\n    ];\n});\n_c = TemplateManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateManagement);\nvar _c;\n$RefreshReg$(_c, \"TemplateManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\n"));

/***/ })

});